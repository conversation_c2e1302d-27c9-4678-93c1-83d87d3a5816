<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Traits\WorkflowLogTraits;
use App\Http\Traits\ResponseTraits;
use App\Http\Traits\RadisCacheTraits;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Traits\FormatingTraits;
use Symfony\Component\Console\Output\BufferedOutput;
use App\Http\Traits\DateTimeFormatTraits;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Traits\SanitizedTraits;

abstract class Workflow extends Command
{
    use WorkflowLogTraits;
    use ResponseTraits;
    use RadisCacheTraits;
    use FormatingTraits;
    use DateTimeFormatTraits;
    use SanitizedTraits;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:workflow {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Base workflow';

    protected $flowId;

    protected $status;

    protected $statusCode = 500;

    protected $message = '';

    protected $config;

    protected $input;

    protected $data = [];

    protected $meta = [];

    protected $rules = [];

    protected $rulesMessage = [];

    protected $request = [];

    protected $pointer = 0;

    protected $cache = [];

    protected $formatter = [];

    protected $formatterByKeys = [];

    protected $mapper = [];

    protected $temp = [];

    protected $smartFormatter = [];

    protected $hugeData = false;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->init();
        try {

            $this->sanitize($this->request);
            // Check if any SQL injection was detected

            if ($this->hasDetectedSQLInjection()) {
                $this->status = 'error';
                $this->statusCode =  400;
                $this->meta['errors'] = $this->getDetectedSQLInjection();
                $this->message = 'Potential SQL injection detected.';
            } else {


                $this->validate();
                // $this->preHook();
                $this->apply();
                // $this->postHook();
                $this->status = 'success';
                $this->statusCode = ($this->statusCode) ? $this->statusCode : 200;
                $this->message = ($this->message != '') ? $this->message : 'Action executed successfully';
            }
            //$this->log($this->flowId, $this->actionId, $this->getNode(), $this->getConnection());
            //  $this->info( $this->success($this->message, $this->statusCode, $this->data, $this->meta, $this->pointer));
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->statusCode = ($e->getCode()) ? $e->getCode() : ($this->statusCode > 299 ? $this->statusCode : 500);
            $this->message = ($e->getMessage()) ? $e->getMessage() : $this->message;
            $this->data = [];
            //$this->log($this->flowId, $this->actionId, $this->getNode(), $this->getConnection());
            //$this->info($this->error($this->message, $this->statusCode, $this->data, $this->meta, $this->pointer));
        }
        $this->final();
    }

    public function init()
    {

        $this->flowId = $this->hashWorkflow();
        $this->input = (array) json_decode($this->argument('input'), true);
        $this->request = $this->input;
        $this->config = $this->getConfig();
        $this->newLog($this->flowId, $this->input);
    }

    public function hashWorkflow()
    {
        $hash = [];
        $hash['time'] = time();
        $hash['signature'] = $this->signature;
        return base64_encode(json_encode($hash));
    }

    public function getConfig()
    {
        return [];
    }

    public function validate()
    {
        $this->action('action:validate', 0, [
            'rules' => $this->rules,
            'rulesMessage' => $this->rulesMessage,
            'data' => $this->request
        ]);

        if ($this->status === 'error') {
            throw new \Exception($this->message, $this->statusCode);
            // $this->log($this->message, $this->statusCode, $this->data, $this->meta);
        }
    }

    public function action($actionName, $parent = 0, $input = [])
    {
        $output = new BufferedOutput();
        \Artisan::call($actionName, [
            'flowId' => $this->flowId,
            'parentId' => $parent,
            'input' => json_encode($input)
        ], $output);
        $output = (array) json_decode($output->fetch(), true);
        if (!empty($output)) {
            $this->data = $output['data'];
            $this->meta = $output['meta'];
            $this->message = $output['message'];
            $this->statusCode = $output['status_code'];
            $this->status = $output['status'];
            $this->pointer = $output['pointer']['actionId'];
        } else {
            $this->data = [];
            $this->meta = [];
            $this->message = 'Error: Unknown action';
            $this->statusCode = 500;
            $this->status = 'error';
            $this->pointer = 0;
        }
        if ($this->status == 'error') {
            throw new \Exception($this->message, (int) $this->statusCode);
        }
        // $this->cache = $this->getFlowCache($this->flowId);
        return $this->data;
    }

    public function final()
    {
        // if ($this->status == 'success') {
        if ($this->statusCode == 200) {


            // $allowPagination = $this->allowPagination ?? true;
            // $allowPagination = true;
            // foreach ($this->data as $key => $value) {

            //     if (!is_array($value)) {
            //         $allowPagination = false;
            //         break;
            //     }
            // }
            // if ($allowPagination) {
            //     $page = $this->input['page'] ?? 1;
            //     // $perPage = $this->hugeData ? 10000 : ($this->input['per_page'] ?? 5);
            //     $perPage = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 10000 : 5);
            //     $pagenationData = $this->paginate($this->data, $perPage, $page);

            //     $pagenationData = $pagenationData->toArray();
            //     $this->data = array_values($pagenationData['data']) ?? [];
            //     unset($pagenationData['data']);
            //     $this->meta['pagination'] = $pagenationData;
            // }

            $output = $this->success($this->message, $this->statusCode, $this->data, $this->meta);
        } else {
            $output = $this->error($this->message, $this->statusCode, $this->data, $this->meta);
        }
        $this->info($output);
        $this->saveLog($this->flowId);
    }

    public function paginate($items, $perPage = 5, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    public function prepare($datasources = [], $withFormat = false, $auto = false)
    {
        if ($auto) {
            $datasources = [];
            foreach ($this->cache as $key => $value) {
                $datasources = $this->cache[$key];
                $this->data = call_user_func_array('array_merge_recursive', $this->data, $datasources);
            }
            //$this->data = $this->format($this->data);
        } else {
            $this->data = call_user_func_array('array_merge_recursive', $datasources);
            if ($withFormat) {
                $this->data = $this->format($this->data);
            }
        }
    }

    public function mergeData($primeryData, $secondaryData)
    {
        foreach ($primeryData as $key => $value) {
            $ele = $value;
            if (!empty($secondaryData[$key])) {
                $ele = array_merge($value, $secondaryData[$key]);
            }
            $primeryData[$key] = $ele;
        }
        return $primeryData;
    }

    public function getCompanyInitials($name)
    {
        $company_initials = '';
        $words = explode(' ', $name);
        foreach ($words as $word) {
            $company_initials .= $word[0];
        }
        return $company_initials;
    }

    public function convertNumberToWords($amount)
    {
        // Check if the amount is negative.
        if ($amount < 0) {
            return '-' . $this->convertNumberToWords(abs($amount));
        }

        $ones = array(
            0 => '',
            1 => 'one',
            2 => 'two',
            3 => 'three',
            4 => 'four',
            5 => 'five',
            6 => 'six',
            7 => 'seven',
            8 => 'eight',
            9 => 'nine',
            10 => 'ten',
            11 => 'eleven',
            12 => 'twelve',
            13 => 'thirteen',
            14 => 'fourteen',
            15 => 'fifteen',
            16 => 'sixteen',
            17 => 'seventeen',
            18 => 'eighteen',
            19 => 'nineteen'
        );

        $tens = array(
            2 => 'twenty',
            3 => 'thirty',
            4 => 'forty',
            5 => 'fifty',
            6 => 'sixty',
            7 => 'seventy',
            8 => 'eighty',
            9 => 'ninety'
        );

        $amount = number_format($amount, 2, '.', '');
        $amount_parts = explode('.', $amount);
        $rupees = (int)$amount_parts[0];
        $paise = (int)$amount_parts[1];

        $rupees_in_words = '';

        if ($rupees > 0) {
            if ($rupees < 20) {
                $rupees_in_words = $ones[$rupees];
            } elseif ($rupees < 100) {
                $rupees_in_words = $tens[floor($rupees / 10)] . ' ' . $ones[$rupees % 10];
            } elseif ($rupees < 1000) {
                $rupees_in_words = $ones[floor($rupees / 100)] . ' hundred ' . $this->convertNumberToWords($rupees % 100);
            } elseif ($rupees < 100000) {
                $rupees_in_words = $this->convertNumberToWords(floor($rupees / 1000)) . ' thousand ' . $this->convertNumberToWords($rupees % 1000);
            } elseif ($rupees < 10000000) {
                $rupees_in_words = $this->convertNumberToWords(floor($rupees / 100000)) . ' lakh ' . $this->convertNumberToWords($rupees % 100000);
            } elseif ($rupees < 1000000000) {
                $rupees_in_words = $this->convertNumberToWords(floor($rupees / 10000000)) . ' crore ' . $this->convertNumberToWords($rupees % 10000000);
            }
        }

        return $rupees_in_words;
    }


    public function hitCURL($mydata, $type, $id)
    {
        $soc_id = $this->input['company_id'];
        $society = DB::connection('master')->table('chsone_societies_master')->select('soc_name', 'soc_address_1', 'soc_address_2', 'soc_landmark', 'soc_city_or_town', 'soc_state', 'soc_pincode')->where('soc_id', $soc_id)->first();

        $soc_address = $society->soc_address_1 . ', ' . $society->soc_address_2 . ', ' . $society->soc_landmark . ', ' . $society->soc_city_or_town . ', ' . $society->soc_state . ', ' . $society->soc_pincode . '.';
        $soc_address = str_replace(', ,', ',', $soc_address);
        $soc_address = str_replace(' ,', ',', $soc_address);

        $mydata['soc_address'] = $soc_address;
        $mydata['soc_name'] = $society->soc_name;

        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'template' => $type,
            'data' => $mydata,
            'file_name' => 'receiptTemplate_' . $timestamp . '_' . $id,
            'file_type' => 'pdf'
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('MICRO_SERVICE_URL') . '/generate-pdf',
            //CURLOPT_URL => env('MICRO_SERVICE_URL') . "/generate-pdf",
            //CURLOPT_URL => "https://socservice.cubeone.in/api" . "generate-pdf",
            //CURLOPT_URL => 'http://127.0.0.1:8001/api/generate-pdf',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        //print curl_error($curl);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function hitCURLForGenerateCSV($mydata, $headings, $filename, $report_heading = [])
    {
        $soc_id = $this->input['company_id'];
        $soc_name = $this->input['additional_data']['society_name'] ?? '';
        $soc_reg_num = $this->input['additional_data']['society_reg_num'] ?? '';
        $society = DB::connection('master')->table('chsone_societies_master')->select('soc_name', 'soc_address_1', 'soc_address_2', 'soc_landmark', 'soc_city_or_town', 'soc_state', 'soc_pincode')->where('soc_id', $soc_id)->first();
        $soc_address = $society->soc_address_1 . ', ' . $society->soc_address_2 . ', ' . $society->soc_landmark . ', ' . $society->soc_city_or_town . ', ' . $society->soc_state . ', ' . $society->soc_pincode . '.';
        $soc_address = str_replace(', ,', ',', $soc_address);
        $soc_address = str_replace(' ,', ',', $soc_address);

        $headings = [
            'report_heading' => [
                'soc_name'      => $soc_name,
                'soc_reg_num'   => 'REGN No.:' . $soc_reg_num,
                'soc_add'       => $soc_address ?? '',
                'report_name'   => $report_heading['report_name'] ?? '',
                'report_date_range'  => $report_heading['report_date_range'] ?? '',
            ],
            'table_heading' => $headings
        ];
        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'file_type' => 'xlsx',
            'data' => $mydata,
            'file_name' => $filename . '' . $timestamp,
            'headings' => $headings
        );

        if (isset($this->input['additional_data']['society_name'])) {
            $postfields['society_name'] = $this->input['additional_data']['society_name'] ?? '';
        }


        $curl = curl_init();

        curl_setopt_array($curl, array(
            // CURLOPT_URL =>'http://************:8001/api/generate-excel',
            CURLOPT_URL => 'http://127.0.0.1:8001/api/generate-excel',

            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        return json_decode($response, true);
    }

    public function hitCURLForGenerateDoubleCSV($mydata, $headings, $filename, $report_heading = [])
    {

        $soc_id = $this->input['company_id'];
        $soc_name = $this->input['additional_data']['society_name'] ?? '';
        $soc_reg_num = $this->input['additional_data']['society_reg_num'] ?? '';
        $society = DB::connection('master')->table('chsone_societies_master')->select('soc_name', 'soc_address_1', 'soc_address_2', 'soc_landmark', 'soc_city_or_town', 'soc_state', 'soc_pincode')->where('soc_id', $soc_id)->first();
        $soc_address = $society->soc_address_1 . ', ' . $society->soc_address_2 . ', ' . $society->soc_landmark . ', ' . $society->soc_city_or_town . ', ' . $society->soc_state . ', ' . $society->soc_pincode . '.';
        $soc_address = str_replace(', ,', ',', $soc_address);
        $soc_address = str_replace(' ,', ',', $soc_address);


        $headings = [
            'report_heading' => [
                'soc_name'      => $soc_name,
                'soc_reg_num'   => 'REGN No.:' . $soc_reg_num,
                'soc_add'       => $soc_address ?? '',
                'report_name'   => $report_heading['report_name'] ?? '',
                'report_date_range'  => $report_heading['report_date_range'] ?? '',
            ],
            'table_heading' => $headings
        ];
        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'file_type' => 'xlsx',
            'data' => $mydata,
            'file_name' => $filename . '' . $timestamp,
            'headings' => $headings
        );

        if (isset($this->input['additional_data']['society_name'])) {
            $postfields['society_name'] = $this->input['additional_data']['society_name'] ?? '';
        }
        $curl = curl_init();

        // dd(vars: $postfields);
        curl_setopt_array($curl, array(
            // CURLOPT_URL => 'http://*************:8001/api/generate-double-excel',

            CURLOPT_URL => env('MICRO_SERVICE_URL') . '/generate-excel',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function hitCURLForGeneratePDF($mydata, $headings, $filename, $headers = [], $summaryHeadings = [], $company_details = [])
    {
        //dd($mydata);

        $headers = [
            'society_name' => $this->input['additional_data']['society_name'] ?? '',
            'society_reg_num' => $this->input['additional_data']['society_reg_num'] ?? '',
        ];
        $company_details['soc_gst'] = $society_reg_num ?? '';
        $company_details['soc_reg'] = $soc_gst_number ?? '';
        $company_details['society_reg_num'] = $this->input['additional_data']['society_reg_num'] ?? '';
        $company_details['soc_gst_number'] = $this->input['additional_data']['soc_gst_number'] ?? '';
        $company_details['society_address'] = $this->input['additional_data']['society_address'] ?? '';
        //dd($mydata);
        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'file_type' => 'pdf',
            'data' => $mydata,
            'file_name' => $filename,
            'headings' => $headings,
            'header' => $headers, // Ensure $headers is passed here
            'summaryHeadings' => $summaryHeadings,
            'company_details' => $company_details,
            'society_name' => $this->input['additional_data']['society_name'] ?? '',
            'society_reg_num' => $this->input['additional_data']['society_reg_num'] ?? ''
        );
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://127.0.0.1:8001/api/download-PDF',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        dd($response);
        if ($response === false) {
            $error_number = curl_errno($curl);
            $error_message = curl_error($curl);
            // Handle error (e.g., log or throw exception)
        }
        curl_close($curl);
        return json_decode($response, true);
    }

    public function hitCURLForGenerateNOC($mydata, $filename)
    {
        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'file_type' => 'pdf',
            'data' => $mydata,
            'file_name' => $filename . '' . $timestamp,
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('MICRO_SERVICE_URL') . '/download-noc',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function hitCURLForGenerateNotice($mydata, $filename)
    {

        $timestamp = date('Y-m-d H:i:s');
        $postfields = array(
            'service_id' => 1,
            'company_id' => 1,
            'user_id' => 1,
            'file_type' => 'pdf',
            'data' => $mydata,
            'file_name' => $filename . '' . $timestamp,
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('MICRO_SERVICE_URL') . '/download-notice',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postfields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function tenantDB()
    {
        // Replace 'tenant' with your actual connection name if needed
        return DB::connection('tenant');
    }



    public function masterDB()
    {
        return DB::connection('master');
    }
}
