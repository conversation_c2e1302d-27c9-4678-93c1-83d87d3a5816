<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class NonMemberAdvancesDownload extends Workflow
{
    use CommonFunctionTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:nonMemberAdvancesDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Non Member Advance Download';

    protected $headings = [
        "name",
        "total_adjustable",
        "total_refundable"
    ];

    protected $summaryHeadings = [
        "total_adjustable",
        "total_refundable"
    ];

    protected $header = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Get data from nonMemberAdvance datasource
            $nonMemberAdvancesList = $this->action('datasource:nonMemberAdvance', $this->pointer, $this->request);
                dd($nonMemberAdvancesList);
            // Format data to match the headings
            $outputData = [];
            foreach ($nonMemberAdvancesList as $item) {
                $outputData[] = [
                    'name' => $item['name'] ?? '',
                    'total_adjustable' => $item['total_adjustable'] ?? 0,
                    'total_refundable' => $item['total_refundable'] ?? 0
                ];
            }
            
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'NonmemberAdvancesReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($outputData, $this->headings, 'NonmemberAdvancesReport_');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
