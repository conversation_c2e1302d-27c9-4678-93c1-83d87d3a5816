<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadMembersWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMembers {input}';

    protected $description = 'Download Members in excel or pdf format';

    protected $formatter = [
        "id" => "",
        "member_id" => "",
        "fk_unit_id" => "",
        "salute" => "",
        "member_name" => "",
        "member_email_id" => "",
        "member_mobile_number" => "",
        "member_effective_date" => "",
        "member_type_name" => "",
        "member_intercom" => "",
        "member_status" => "",
        "status" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "building_unit" => "",
        "approved" => ""
    ];

    protected $formatterByKeys = ["id"];

    protected $headings = [
        'id' => 'Sr No.',
        'member_id' => 'Member ID',
        'fk_unit_id' => 'Unit ID',
        'salute' => 'Salutation',
        'member_name' => 'Member Name',
        'member_email_id' => 'Email ID',
        'member_mobile_number' => 'Mobile Number',
        'member_effective_date' => 'Effective Date',
        'member_type_name' => 'Member Type',
        'member_intercom' => 'Intercom',
        'member_status' => 'Member Status',
        'status' => 'Status',
        'soc_building_name' => 'Building Name',
        'unit_flat_number' => 'Unit/Flat Number',
        'building_unit' => 'Building Unit',
        'approved' => 'Approved'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type') {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $allottees = $this->action('datasource:allottees', $this->pointer, $this->request);
            dd($allottees);
            $this->data = [];

            if($type == 'excel') {
                $data = $this->hitCURLForGenerateCSV($allottees, $this->headings, 'allottee_');
                $this->data['url'] = $data['data'];
            } else {
                $data = $this->hitCURLForGeneratePDF($allottees, $this->headings, 'allottees');
                $this->data['url'] = $data['data'];
            }
        }
    }
}