<?php

namespace App\Console\Commands\Workflows\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadParkingAllotmentsWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadParkingAllotments {input}';
    
    protected $description = 'Download Parking Allotments in Excel or PDF';

    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    protected $headings = [
        "id" => "Sr No.",
        "parking_number" => "Parking Number",
        "allotment_to" => "Allotment To",
        "parking_type" => "Parking Type",
        "effective_date" => "Effective Date",
        "allowed_number_of_parkings" => "Allowed Parkings",
        "actual_number_of_parkings" => "Actual Parkings",
        "allotment_for" => "Allotment For",
        "member_primary_name" => "Member Name",
        "status" => "Status"
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $parkingAllotmentList = $this->action('datasource:parkingAllotmentList', $this->pointer, $this->request);
            $this->data = [];

            // Transform the data to match our headings
            $count = 0;
            $newData = [];

            foreach ($parkingAllotmentList as $parking) {
                $count++;

                $newData[] = [
                    'id' => $count,
                    'parking_number' => $parking['parking_number'] ?? '',
                    'allotment_to' => $parking['allotment_to'] ?? '',
                    'parking_type' => $parking['parking_type'] ?? '',
                    'effective_date' => $parking['effective_date'] ?? '',
                    'allowed_number_of_parkings' => $parking['allowed_number_of_parkings'] ?? '',
                    'actual_number_of_parkings' => $parking['actual_number_of_parkings'] ?? '',
                    'allotment_for' => $parking['allotment_for'] ?? '',
                    'member_primary_name' => $parking['member_primary_name'] ?? '',
                    'status' => $parking['status'] ?? '',
                ];
            }

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'parkingAllotment_');
                $this->data['url'] = $data['data'];
            }
            else
            {
                $data = $this->hitCURLForGeneratePDF($newData, $this->headings, 'parkingAllotment');
                $this->data['url'] = $data['data'];
            }
        }
    }

    public function concat($a, $b, $c=null)
    {
        if (!is_null($c)) {
            $c = ucfirst(str_replace('wheeler', ' Wheeler', $c));
            return "Allowed: ".$a . ', Actual: ' . $b. ", Space for: ". $c;
        } elseif (empty($b)) {
            return $a ." onwards";
        } else {
            return $a ." / ".$b;
        }
    }
}