<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class DownloadTransactionWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadTransaction {input}';

    protected $description = 'Download Transaction in excel or pdf format';

    protected $headings = [
        'Date' => 'Date',
        'Ledger Account' => 'Ledger Account',
        'Narration' => 'Narration',
        'Debit' => 'Debit',
        'Credit' => 'Credit',
        'Balance' => 'Balance',
    ];

    public function apply()
    {
        $month = $this->input['filters']['month'] ?? date('m');
        if ($month != null && ($month < 1 || $month > 12)) {
            $this->status = 'error';
            $this->meta['errors']['month'] = 'Month must be between 1 and 12';
            $this->statusCode = 400;
            return;
        }

        $year = $this->input['filters']['year'] ?? date('Y');
        $currentYear = date('Y'); // Get the current year

        if ($year != null && (!is_numeric($year) || $year < 1900 || $year > ($currentYear + 1))) {
            $this->status = 'error';
            $this->meta['errors']['year'] = 'Year must be a valid four-digit year between 1900 and ' . ($currentYear + 1);
            $this->statusCode = 400;
            return;
        }
        $listTransactionMonthly = $this->action('datasource:listTransactionMonthly', $this->pointer, $this->request);
        $currentRoute = Route::current();

        // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();
        //dd($routeUri);
        if (strpos($routeUri, 'transaction/downloadTransactionMonthly') !== false) {
            $listTransactionMonthly['transactions'] = $listTransactionMonthly[1];
        }
        if ($listTransactionMonthly['transactions']) {
            // dd($listTransactionMonthly['transactions']);

            //create a new array which only return selected field which present in heading keys
            $listTransactionMonthly['transactions'] = array_map(
                function ($transaction) {
                    $newObj = [
                        'Date' => $transaction['transaction_date'],
                        'Ledger Account' => $transaction['counter_ledger_account_name'],
                        'Narration' => $transaction['memo_desc'],
                        'Debit' => str($transaction['is_opening_balance']),
                        'Credit' => $transaction['transaction_amount'],
                        'Balance' => $transaction['balance'],
                    ];
                    return $newObj;
                },
                $listTransactionMonthly['transactions'],
            );

            //add one more entry which calculate total of all transaction amount
            $total = array_reduce(
                $listTransactionMonthly['transactions'],
                function ($carry, $transaction) {
                    $carry['Balance']  += $transaction['Balance'];
                    return $carry;
                },
                ['Debit' => 0, 'Credit' => 0, 'Balance' => 0]
            );

             $newObj2 = [
                'Date' => "Total",
                'Ledger Account' =>'',
                'Narration' => '',
                'Debit' => '',
                'Credit' => '',
                'Balance' => $total['Balance'],
            ];

            $newArray = array_merge($listTransactionMonthly['transactions'], [$newObj2]);

            $data = $this->hitCURLForGenerateCSV($newArray, $this->headings, 'transactions_');
            $this->data['url'] = $data['data'];
        }
    }
}
